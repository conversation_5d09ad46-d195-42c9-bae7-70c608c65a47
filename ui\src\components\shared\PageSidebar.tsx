import React, { useState, useEffect } from 'react';
import { <PERSON>File, FiPlus, FiX, FiLoader } from 'react-icons/fi';

// Page interface from pageGenService
export interface Page {
  id: number;
  project_id: number;
  title: string;
  url?: string;
  html?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface Project {
  id: number;
  title: string;
  description?: string;
  created_at: string;
  updated_at: string;
  preview_image_url?: string;
  status: string;
  type: string;
}

export interface PageSidebarProps {
  // Core functionality
  isOpen: boolean;
  onClose: () => void;
  
  // Data
  project: Project | null;
  pages: Page[];
  currentPageId?: number | string | null;
  
  // Loading states
  isLoadingPages?: boolean;
  isLoadingPage?: boolean;
  
  // Event handlers
  onPageSelect: (page: Page) => void;
  onCreatePage: () => void;
  onPageRename?: (pageId: number, newName: string) => Promise<void>;
  onPageDelete?: (pageId: number) => Promise<void>;
  onLinkPages?: () => void;
  
  // UI customization
  theme?: 'blue' | 'violet';
  variant?: 'overlay' | 'inline';
  showNewPageButton?: boolean;
  showPageActions?: boolean;
  
  // Additional features
  error?: string;
  onRetry?: () => void;
}

export const PageSidebar: React.FC<PageSidebarProps> = ({
  isOpen,
  onClose,
  project,
  pages,
  currentPageId,
  isLoadingPages = false,
  isLoadingPage = false,
  onPageSelect,
  onCreatePage,
  onPageRename,
  onPageDelete,
  onLinkPages,
  theme = 'violet',
  variant = 'overlay',
  showNewPageButton = true,
  showPageActions = true,
  error,
  onRetry,
}) => {
  const [renamingPageId, setRenamingPageId] = useState<number | null>(null);
  const [newPageName, setNewPageName] = useState('');
  const [openDropdownId, setOpenDropdownId] = useState<number | null>(null);

  const themeColors = {
    blue: {
      primary: 'blue-600',
      primaryHover: 'blue-700',
      accent: 'blue-50',
      accentBorder: 'blue-200',
      accentText: 'blue-900'
    },
    violet: {
      primary: 'violet-600',
      primaryHover: 'violet-700',
      accent: 'violet-50',
      accentBorder: 'violet-200',
      accentText: 'violet-900'
    }
  };

  const colors = themeColors[theme];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.dropdown-button') && !target.closest('.dropdown-menu')) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const startRename = (page: Page) => {
    setRenamingPageId(page.id);
    setNewPageName(page.title || `Page ${page.id}`);
    setOpenDropdownId(null);
  };

  const cancelRename = () => {
    setRenamingPageId(null);
    setNewPageName('');
  };

  const confirmRename = async (pageId: number) => {
    if (onPageRename && newPageName.trim()) {
      try {
        await onPageRename(pageId, newPageName.trim());
        setRenamingPageId(null);
        setNewPageName('');
      } catch (error) {
        console.error('Error renaming page:', error);
        // Keep rename mode active on error
      }
    }
  };

  const startDelete = async (pageId: number) => {
    if (onPageDelete && window.confirm('Are you sure you want to delete this page?')) {
      try {
        await onPageDelete(pageId);
        setOpenDropdownId(null);
      } catch (error) {
        console.error('Error deleting page:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'editing':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return `bg-${colors.accent} text-${colors.accentText}`;
    }
  };

  // Inline variant - no overlay, just the sidebar content
  if (variant === 'inline') {
    return (
      <div className={`bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ${isOpen ? 'w-80' : 'w-0 opacity-0 overflow-hidden'}`}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex justify-between items-center p-4 border-b border-gray-100">
            <div className="flex items-center space-x-2">
              <FiFile className="w-5 h-5 text-gray-500" />
              <div>
                {/* <h3 className="font-medium text-gray-900">Pages</h3> */}
                {project && (
                  <p className="text-xs text-gray-500 truncate">{project.title}</p>
                )}
              </div>
            </div>
            {showNewPageButton && (
              <button
                onClick={onCreatePage}
                className={`flex items-center justify-center px-4 py-2 bg-${colors.primary} text-white rounded-xl hover:bg-${colors.primaryHover} transition-colors font-medium shadow-sm`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                New Page
              </button>
            )}
          </div>

          {/* Pages List */}
          <div className="flex-1 overflow-y-auto">
            {isLoadingPages ? (
              <div className="flex items-center justify-center py-8">
                <div className={`w-5 h-5 border-2 border-${colors.primary} border-t-transparent rounded-full animate-spin mr-3`}></div>
                <span className="text-sm text-gray-500">Loading pages...</span>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-sm text-red-600">{error}</p>
                {onRetry && (
                  <button
                    onClick={onRetry}
                    className="mt-2 text-sm text-blue-600 hover:text-blue-700"
                  >
                    Try again
                  </button>
                )}
              </div>
            ) : pages.length > 0 ? (
              <div className="space-y-2 p-4">
                {pages.map((page) => (
                  <div key={page.id} className="relative">
                    {renamingPageId === page.id ? (
                      /* Rename Input */
                      <div className="p-3 bg-gray-50 border border-gray-200 rounded-xl">
                        <input
                          type="text"
                          value={newPageName}
                          onChange={(e) => setNewPageName(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              confirmRename(page.id);
                            } else if (e.key === 'Escape') {
                              cancelRename();
                            }
                          }}
                          className={`w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-${colors.primary}`}
                          autoFocus
                        />
                        <div className="flex space-x-2 mt-2">
                          <button
                            onClick={() => confirmRename(page.id)}
                            className="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                          >
                            Save
                          </button>
                          <button
                            onClick={cancelRename}
                            className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      /* Page Item */
                      <div className={`group relative rounded-xl border transition-all ${
                        currentPageId === page.id
                          ? `bg-${colors.accent} border-${colors.accentBorder} shadow-sm`
                          : 'bg-white border-gray-100 hover:border-gray-200 hover:shadow-sm'
                      }`}>
                        <button
                          onClick={() => onPageSelect(page)}
                          disabled={isLoadingPage}
                          className={`w-full text-left p-3 rounded-xl transition-colors ${
                            isLoadingPage ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <h3 className={`text-sm font-medium truncate ${
                                currentPageId === page.id ? `text-${colors.accentText}` : 'text-gray-900'
                              }`}>
                                {page.title || `Page ${page.id}`}
                              </h3>
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-xs text-gray-400">
                                  {formatDate(page.updated_at)}
                                </span>
                              </div>
                            </div>
                            {currentPageId === page.id && (
                              <div className={`ml-2 w-2 h-2 bg-${colors.primary} rounded-full`}></div>
                            )}
                          </div>
                        </button>

                        {/* Dropdown Menu */}
                        {showPageActions && (onPageRename || onPageDelete) && (
                          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenDropdownId(openDropdownId === page.id ? null : page.id);
                              }}
                              className="p-1 text-gray-400 hover:text-gray-600 rounded dropdown-button"
                            >
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                              </svg>
                            </button>

                            {openDropdownId === page.id && (
                              <div className="absolute right-0 top-6 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10 dropdown-menu">
                                {onPageRename && (
                                  <button
                                    onClick={() => startRename(page)}
                                    className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-t-lg"
                                  >
                                    Rename
                                  </button>
                                )}
                                {onPageDelete && (
                                  <button
                                    onClick={() => startDelete(page.id)}
                                    className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-b-lg"
                                  >
                                    Delete
                                  </button>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-500">No pages yet</p>
                <p className="text-xs text-gray-400 mt-1">
                  Create your first page to get started
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Overlay variant - with backdrop and fixed positioning
  return (
    <>
      {/* Overlay - only show on mobile and only when sidebar is open */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed left-0 top-0 h-full w-80 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>

        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <FiFile className="w-5 h-5 text-gray-500" />
            <div>
              <h3 className="font-medium text-gray-900">Pages</h3>
              {project && (
                <p className="text-xs text-gray-500 truncate">{project.title}</p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors lg:hidden"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Create New Page Button */}
          {showNewPageButton && (
            <div className="p-4 border-b border-gray-200">
              <button
                onClick={onCreatePage}
                className={`w-full flex items-center justify-center space-x-2 px-3 py-2 bg-${colors.primary} text-white rounded-md hover:bg-${colors.primaryHover} transition-colors`}
              >
                <FiPlus className="w-4 h-4" />
                <span>New Page</span>
              </button>
            </div>
          )}

          {/* Pages List */}
          <div className="p-4">
            {isLoadingPages ? (
              <div className="flex items-center justify-center py-8">
                <FiLoader className="w-5 h-5 animate-spin text-gray-400" />
                <span className="ml-2 text-sm text-gray-500">Loading pages...</span>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-sm text-red-600">{error}</p>
                {onRetry && (
                  <button
                    onClick={onRetry}
                    className="mt-2 text-sm text-blue-600 hover:text-blue-700"
                  >
                    Try again
                  </button>
                )}
              </div>
            ) : pages.length > 0 ? (
              <div className="space-y-2">
                {pages.map((page) => (
                  <button
                    key={page.id}
                    onClick={() => onPageSelect(page)}
                    className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {page.title || `Page ${page.id}`}
                        </h4>
                        {page.url && (
                          <p className="text-xs text-gray-500 truncate mt-1">
                            {page.url}
                          </p>
                        )}
                        <p className="text-xs text-gray-400 mt-1">
                          {formatDate(page.updated_at)}
                        </p>
                      </div>
                     
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiFile className="w-8 h-8 text-gray-300 mx-auto mb-3" />
                <p className="text-sm text-gray-500">No pages yet</p>
                <p className="text-xs text-gray-400 mt-1">
                  Create your first page to get started
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
