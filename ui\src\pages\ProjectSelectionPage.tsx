import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>Folder, FiLoader, FiAlertCircle, FiZap, FiMoreVertical, FiEdit2, FiTrash2 } from 'react-icons/fi';
import { Project, getProjectList, createProject, updateProject, deleteProject } from '../services/pageGenService';
import { CreateProjectModal } from '../components/CreateProjectModal';

/**
 * ProjectSelectionPage - Modern project selection interface
 * Displays user's projects and allows project creation and selection
 */
export function ProjectSelectionPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState<number | null>(null);
  const [renamingProjectId, setRenamingProjectId] = useState<number | null>(null);
  const [newProjectName, setNewProjectName] = useState('');
  const [deletingProjectId, setDeletingProjectId] = useState<number | null>(null);
  const [feedbackMessage, setFeedbackMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const navigate = useNavigate();

  // Fetch projects on component mount
  useEffect(() => {
    fetchProjects();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdownId !== null) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openDropdownId]);

  // Auto-hide feedback messages after 5 seconds
  useEffect(() => {
    if (feedbackMessage) {
      const timer = setTimeout(() => {
        setFeedbackMessage(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [feedbackMessage]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch first page with reasonable page size
      const response = await getProjectList(1, 20);
      setProjects(response.projects);
    } catch (err: any) {
      console.error('Error fetching projects:', err);
      setError('Failed to load projects. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleProjectSelect = async (project: Project) => {
    if (isNavigating) return; // Prevent double-clicks

    setSelectedProject(project);
    setIsNavigating(true);

    try {
      // Navigate to the editor with the selected project
      navigate(`/editor-v3-refactored/${project.id}`);
    } catch (err) {
      console.error('Error navigating to project:', err);
      setIsNavigating(false);
      setSelectedProject(null);
    }
  };

  const handleCreateProject = () => {
    setShowCreateModal(true);
  };

  const handleProjectCreated = (project: Project) => {
    // Add the new project to the list
    setProjects(prev => [project, ...prev]);
    setShowCreateModal(false);
    // Navigate to the new project
    navigate(`/editor-v3-refactored/${project.id}`);
  };

  const handleRename = (project: Project) => {
    setRenamingProjectId(project.id);
    setNewProjectName(project.title);
    setOpenDropdownId(null);
    setFeedbackMessage(null);
  };

  const handleRenameSubmit = async (projectId: number) => {
    if (!newProjectName.trim()) {
      setFeedbackMessage({ type: 'error', message: 'Project name cannot be empty' });
      return;
    }

    if (newProjectName.trim().length > 250) {
      setFeedbackMessage({ type: 'error', message: 'Project name must be 250 characters or less' });
      return;
    }

    try {
      const response = await updateProject(projectId, {
        title: newProjectName.trim()
      });

      if (response.success) {
        setProjects(prev => prev.map(p =>
          p.id === projectId ? { ...p, title: newProjectName.trim() } : p
        ));
        setFeedbackMessage({ type: 'success', message: 'Project renamed successfully' });
      } else {
        setFeedbackMessage({ type: 'error', message: 'Failed to rename project' });
      }
    } catch (error) {
      console.error('Error renaming project:', error);
      setFeedbackMessage({ type: 'error', message: 'Failed to rename project. Please try again.' });
    } finally {
      setRenamingProjectId(null);
      setNewProjectName('');
    }
  };

  const handleRenameCancel = () => {
    setRenamingProjectId(null);
    setNewProjectName('');
    setFeedbackMessage(null);
  };

  const handleDelete = async (projectId: number) => {
    const project = projects.find(p => p.id === projectId);
    if (!window.confirm(`Are you sure you want to delete "${project?.title}"? This action cannot be undone.`)) {
      return;
    }

    setDeletingProjectId(projectId);
    setOpenDropdownId(null);
    setFeedbackMessage(null);

    try {
      const response = await deleteProject(projectId);
      if (response.success) {
        setProjects(prev => prev.filter(p => p.id !== projectId));
        setFeedbackMessage({ type: 'success', message: 'Project deleted successfully' });
      } else {
        setFeedbackMessage({ type: 'error', message: 'Failed to delete project' });
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      setFeedbackMessage({ type: 'error', message: 'Failed to delete project. Please try again.' });
    } finally {
      setDeletingProjectId(null);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
    } catch (e) {
      return 'Unknown date';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">My Projects</h1>
              <p className="text-sm text-gray-600 mt-1">
                Select a project to continue or create a new one
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => navigate('/prompt-v3')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiZap className="w-4 h-4 mr-2" />
                Quick Create
              </button>
              <button
                onClick={handleCreateProject}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiPlus className="w-4 h-4 mr-2" />
                New Project
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Message */}
      {feedbackMessage && (
        <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4`}>
          <div className={`rounded-md p-4 ${
            feedbackMessage.type === 'success'
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {feedbackMessage.type === 'success' ? (
                  <FiZap className="w-5 h-5 text-green-600 mr-2" />
                ) : (
                  <FiAlertCircle className="w-5 h-5 text-red-600 mr-2" />
                )}
                <p className={`text-sm font-medium ${
                  feedbackMessage.type === 'success' ? 'text-green-800' : 'text-red-800'
                }`}>
                  {feedbackMessage.message}
                </p>
              </div>
              <button
                onClick={() => setFeedbackMessage(null)}
                className={`text-sm ${
                  feedbackMessage.type === 'success' ? 'text-green-600 hover:text-green-800' : 'text-red-600 hover:text-red-800'
                }`}
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          /* Loading State */
          <div className="flex flex-col items-center justify-center py-12">
            <FiLoader className="w-8 h-8 text-blue-600 animate-spin mb-4" />
            <p className="text-gray-600">Loading your projects...</p>
          </div>
        ) : error ? (
          /* Error State */
          <div className="flex flex-col items-center justify-center py-12">
            <FiAlertCircle className="w-8 h-8 text-red-500 mb-4" />
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={fetchProjects}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        ) : projects.length === 0 ? (
          /* Empty State */
          <div className="text-center py-12">
            <FiFolder className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
            <p className="text-gray-600 mb-6">
              Create your first project to get started with building prototypes
            </p>
            <div className="flex justify-center space-x-3">
              <button
                onClick={() => navigate('/prompt-v3')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <FiZap className="w-4 h-4 mr-2" />
                Quick Create
              </button>
              <button
                onClick={handleCreateProject}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                <FiPlus className="w-4 h-4 mr-2" />
                Create Project
              </button>
            </div>
          </div>
        ) : (
          /* Projects Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Projects ({projects.length})</h3>
            </div>

            {/* Responsive Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Project Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                      Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                      Created
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                      Updated
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {projects.map((project) => (
                    <tr key={project.id} className="hover:bg-gray-50 transition-colors">
                      {renamingProjectId === project.id ? (
                        /* Rename Row */
                        <td colSpan={5} className="px-6 py-4 bg-blue-50">
                          <div className="flex items-center space-x-3">
                            <FiFolder className="w-5 h-5 text-gray-400 flex-shrink-0" />
                            <input
                              type="text"
                              value={newProjectName}
                              onChange={(e) => setNewProjectName(e.target.value)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleRenameSubmit(project.id);
                                } else if (e.key === 'Escape') {
                                  handleRenameCancel();
                                }
                              }}
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              autoFocus
                              placeholder="Enter project name..."
                            />
                            <button
                              onClick={() => handleRenameSubmit(project.id)}
                              disabled={!newProjectName.trim()}
                              className="px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              Save
                            </button>
                            <button
                              onClick={handleRenameCancel}
                              className="px-3 py-2 bg-gray-500 text-white text-sm rounded-md hover:bg-gray-600"
                            >
                              Cancel
                            </button>
                          </div>
                        </td>
                      ) : (
                        /* Normal Project Row */
                        <>
                          {/* Project Name Column */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div
                              className="flex items-center cursor-pointer group"
                              onClick={() => handleProjectSelect(project)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                  e.preventDefault();
                                  handleProjectSelect(project);
                                }
                              }}
                              tabIndex={0}
                              role="button"
                              aria-label={`Open project ${project.title}`}
                            >
                              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors mr-3">
                                <FiFolder className="w-4 h-4 text-blue-600" />
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="text-sm font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                                  {project.title}
                                </div>
                                {project.description && (
                                  <div className="text-sm text-gray-500 truncate sm:hidden">
                                    {project.description}
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>

                          {/* Type Column */}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize hidden sm:table-cell">
                            {project.type}
                          </td>

                          {/* Created Date Column */}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">
                            {formatDate(project.created_at)}
                          </td>

                          {/* Updated Date Column */}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden lg:table-cell">
                            {formatDate(project.updated_at)}
                          </td>

                          {/* Actions Column */}
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="relative inline-block">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setOpenDropdownId(openDropdownId === project.id ? null : project.id);
                                }}
                                disabled={deletingProjectId === project.id}
                                className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
                                aria-label="Project actions"
                              >
                                {deletingProjectId === project.id ? (
                                  <FiLoader className="w-5 h-5 animate-spin" />
                                ) : (
                                  <FiMoreVertical className="w-5 h-5" />
                                )}
                              </button>

                              {openDropdownId === project.id && (
                                <div
                                  className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10"
                                  role="menu"
                                  aria-label="Project actions menu"
                                >
                                  <button
                                    onClick={() => handleRename(project)}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2 focus:outline-none focus:bg-gray-50"
                                    role="menuitem"
                                    aria-label={`Rename project ${project.title}`}
                                  >
                                    <FiEdit2 className="w-4 h-4" />
                                    <span>Rename Project</span>
                                  </button>
                                  <button
                                    onClick={() => handleDelete(project.id)}
                                    className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 focus:outline-none focus:bg-red-50"
                                    role="menuitem"
                                    aria-label={`Delete project ${project.title}`}
                                  >
                                    <FiTrash2 className="w-4 h-4" />
                                    <span>Delete Project</span>
                                  </button>
                                </div>
                              )}
                            </div>
                          </td>
                        </>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onProjectCreated={handleProjectCreated}
      />
    </div>
  );
}
